#!/usr/bin/env python3
"""
贪吃蛇游戏启动器
这个脚本确保使用正确的Python环境来运行游戏
"""

import sys
import subprocess
import os

def main():
    print("🐍 贪吃蛇游戏启动器")
    print("=" * 30)
    
    # 检查pygame是否可用
    try:
        import pygame
        print(f"✅ pygame已安装，版本: {pygame.version.ver}")
    except ImportError:
        print("❌ pygame未安装，正在尝试安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pygame"])
            print("✅ pygame安装成功！")
        except subprocess.CalledProcessError:
            print("❌ pygame安装失败，请手动安装: pip install pygame")
            return
    
    # 检查游戏文件是否存在
    if not os.path.exists("snake_game.py"):
        print("❌ 找不到snake_game.py文件")
        return
    
    print("🎮 启动游戏...")
    print("=" * 30)
    
    # 启动游戏
    try:
        subprocess.run([sys.executable, "snake_game.py"])
    except KeyboardInterrupt:
        print("\n👋 游戏已退出")
    except Exception as e:
        print(f"❌ 启动游戏时出错: {e}")

if __name__ == "__main__":
    main()
