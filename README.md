# 贪吃蛇游戏

一个用Python和Pygame开发的经典贪吃蛇游戏。

## 功能特点

- 经典的贪吃蛇游戏玩法
- 使用方向键或WASD键控制蛇的移动
- 实时显示分数
- 游戏结束后按任意键重新开始
- 防止蛇反向移动的安全机制
- 食物不会生成在蛇身上

## 安装和运行

1. 确保您的系统已安装Python 3.6或更高版本

2. 安装依赖包：
```bash
pip install -r requirements.txt
```

3. 运行游戏：
```bash
python snake_game.py
```

## 游戏控制

- **移动控制**：
  - 方向键：↑↓←→
  - 或者使用WASD键：W(上) S(下) A(左) D(右)

- **游戏重新开始**：
  - 游戏结束后按任意键重新开始

## 游戏规则

1. 控制蛇吃红色的食物
2. 每吃一个食物得10分，蛇身会变长
3. 撞墙或撞到自己的身体游戏结束
4. 游戏结束后可以按任意键重新开始

## 技术实现

- 使用Pygame库进行图形渲染和事件处理
- 面向对象设计，包含Snake、Food和Game类
- 游戏循环包含事件处理、游戏逻辑更新和画面渲染
- 碰撞检测和游戏状态管理

享受游戏吧！
