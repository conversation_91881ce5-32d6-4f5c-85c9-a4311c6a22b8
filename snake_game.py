import pygame 
import random
import sys

# 初始化pygame
pygame.init()

# 游戏常量
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
CELL_SIZE = 20
CELL_NUMBER_X = WINDOW_WIDTH // CELL_SIZE
CELL_NUMBER_Y = WINDOW_HEIGHT // CELL_SIZE

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
GRAY = (128, 128, 128)

# 方向常量
UP = (0, -1)
DOWN = (0, 1)
LEFT = (-1, 0)
RIGHT = (1, 0)

class Snake:
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.body = [(CELL_NUMBER_X // 2, CELL_NUMBER_Y // 2)]
        self.direction = RIGHT
        self.grow = False
    
    def move(self):
        head_x, head_y = self.body[0]
        new_head_x = head_x + self.direction[0]
        new_head_y = head_y + self.direction[1]

        # 处理边界穿越（从一边出现到另一边）
        if new_head_x < 0:
            new_head_x = CELL_NUMBER_X - 1
        elif new_head_x >= CELL_NUMBER_X:
            new_head_x = 0

        if new_head_y < 0:
            new_head_y = CELL_NUMBER_Y - 1
        elif new_head_y >= CELL_NUMBER_Y:
            new_head_y = 0

        new_head = (new_head_x, new_head_y)

        if self.grow:
            self.body.insert(0, new_head)
            self.grow = False
        else:
            self.body.insert(0, new_head)
            self.body.pop()
    
    def change_direction(self, new_direction):
        # 防止蛇反向移动
        if (self.direction[0] * -1, self.direction[1] * -1) != new_direction:
            self.direction = new_direction
    
    def check_collision(self):
        head_x, head_y = self.body[0]

        # 只检查是否撞到自己（移除了边界检查，因为现在可以穿墙）
        if (head_x, head_y) in self.body[1:]:
            return True

        return False
    
    def eat_food(self, food_pos):
        if self.body[0] == food_pos:
            self.grow = True
            return True
        return False
    
    def draw(self, screen):
        for segment in self.body:
            x = segment[0] * CELL_SIZE
            y = segment[1] * CELL_SIZE
            rect = pygame.Rect(x, y, CELL_SIZE, CELL_SIZE)
            pygame.draw.rect(screen, GREEN, rect)
            pygame.draw.rect(screen, BLACK, rect, 2)

class Food:
    def __init__(self):
        self.generate_new_position()
    
    def generate_new_position(self):
        self.x = random.randint(0, CELL_NUMBER_X - 1)
        self.y = random.randint(0, CELL_NUMBER_Y - 1)
    
    def draw(self, screen):
        x = self.x * CELL_SIZE
        y = self.y * CELL_SIZE
        rect = pygame.Rect(x, y, CELL_SIZE, CELL_SIZE)
        pygame.draw.rect(screen, RED, rect)

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("贪吃蛇游戏")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.big_font = pygame.font.Font(None, 72)
        self.reset_game()
    
    def reset_game(self):
        self.snake = Snake()
        self.food = Food()
        self.score = 0
        self.game_over = False
    
    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            
            if event.type == pygame.KEYDOWN:
                if self.game_over:
                    # 游戏结束时按任意键重新开始
                    self.reset_game()
                else:
                    # 游戏进行中的方向控制
                    if event.key == pygame.K_UP or event.key == pygame.K_w:
                        self.snake.change_direction(UP)
                    elif event.key == pygame.K_DOWN or event.key == pygame.K_s:
                        self.snake.change_direction(DOWN)
                    elif event.key == pygame.K_LEFT or event.key == pygame.K_a:
                        self.snake.change_direction(LEFT)
                    elif event.key == pygame.K_RIGHT or event.key == pygame.K_d:
                        self.snake.change_direction(RIGHT)
        
        return True
    
    def update(self):
        if not self.game_over:
            self.snake.move()
            
            # 检查碰撞
            if self.snake.check_collision():
                self.game_over = True
            
            # 检查是否吃到食物
            if self.snake.eat_food((self.food.x, self.food.y)):
                self.score += 10
                self.food.generate_new_position()
                # 确保食物不会生成在蛇身上
                while (self.food.x, self.food.y) in self.snake.body:
                    self.food.generate_new_position()
    
    def draw(self):
        self.screen.fill(BLACK)
        
        if not self.game_over:
            # 绘制游戏元素
            self.snake.draw(self.screen)
            self.food.draw(self.screen)
            
            # 绘制分数
            score_text = self.font.render(f"分数: {self.score}", True, WHITE)
            self.screen.blit(score_text, (10, 10))
            
            # 绘制控制说明
            control_text = self.font.render("使用方向键或WASD控制 | 可穿墙", True, WHITE)
            self.screen.blit(control_text, (10, WINDOW_HEIGHT - 30))
        else:
            # 绘制游戏结束画面
            game_over_text = self.big_font.render("游戏结束!", True, RED)
            final_score_text = self.font.render(f"最终分数: {self.score}", True, WHITE)
            restart_text = self.font.render("按任意键重新开始", True, WHITE)
            
            # 居中显示文本
            game_over_rect = game_over_text.get_rect(center=(WINDOW_WIDTH//2, WINDOW_HEIGHT//2 - 50))
            score_rect = final_score_text.get_rect(center=(WINDOW_WIDTH//2, WINDOW_HEIGHT//2))
            restart_rect = restart_text.get_rect(center=(WINDOW_WIDTH//2, WINDOW_HEIGHT//2 + 50))
            
            self.screen.blit(game_over_text, game_over_rect)
            self.screen.blit(final_score_text, score_rect)
            self.screen.blit(restart_text, restart_rect)
        
        pygame.display.flip()
    
    def run(self):
        running = True
        while running:
            running = self.handle_events()
            self.update()
            self.draw()
            self.clock.tick(10)  # 控制游戏速度
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = Game()
    game.run()
